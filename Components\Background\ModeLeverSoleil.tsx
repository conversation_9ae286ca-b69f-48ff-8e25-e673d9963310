import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
// 🌟 CISCO: ÉTOILES UNIFIÉES - SYSTÈME COMPLET
import UnifiedStars from './UnifiedStars';

interface ModeLeverSoleilProps {
  isActive: boolean;
  timerDuration?: number; // Durée du temporisateur en secondes (défaut: 120s)
}

/**
 * 🌅 CISCO: SYSTÈME TIMELINE AVEC KEYFRAMES PRÉCISES
 *
 * ⏱️ KEYFRAMES OFFICIELLES CISCO (Chronomètre précis) :
 * - 00:00:56.352 = Lune coucher (passe derrière le paysage)
 * - 00:01:31.643 = Soleil lever (apparition au dessus du paysage)
 *
 * SYSTÈME RÉVOLUTIONNAIRE :
 * - Une seule timeline maître qui orchestre TOUT
 * - Keyframes temporels précis selon chronomètre Cisco
 * - Synchronisation parfaite de tous les éléments
 * - Trajectoire courbe du soleil (pas tout droit)
 * - Contrôle exact du timing
 */
const ModeLeverSoleil: React.FC<ModeLeverSoleilProps> = ({
  isActive,
  timerDuration = 120 // 120s VITESSE NORMALE
}) => {
  // 🎯 RÉFÉRENCES POUR LES ÉLÉMENTS
  const containerRef = useRef<HTMLDivElement>(null);
  const sunRef = useRef<HTMLDivElement>(null);
  const moonRef = useRef<HTMLImageElement>(null);
  const starsContainerRef = useRef<HTMLDivElement>(null);
  const globalLightRef = useRef<HTMLDivElement>(null);
  const masterTimelineRef = useRef<gsap.core.Timeline | null>(null);
  
  // 🎵 Références audio multiples pour mixer les ambiances
  const nightAudioRefs = useRef<HTMLAudioElement[]>([]);
  const sunriseAudioRefs = useRef<HTMLAudioElement[]>([]);

  // 🌅 CISCO: DÉGRADÉ LEVER DE SOLEIL - Rose en bas, bleu en haut
  const SUNRISE_COLORS = {
    // Début : Nuit profonde
    NIGHT_TOP: '#01040eff',        // Bleu nuit profond (top)
    NIGHT_BOTTOM: '#162546ff',     // Bleu nuit profond (bottom)
    // Lever : Rose en bas, bleu clair en haut
    SUNRISE_TOP: '#4682B4',      // Bleu acier (top)
    SUNRISE_BOTTOM: '#f0c3fcff',   // Rose (bottom)
    // Jour : Bleu ciel
    DAY_TOP: '#3290e9ff',          // Bleu ciel clair (top)
    DAY_BOTTOM: '#e0f3ffff'        // Bleu très clair (bottom)
  };

  // 🎵 GESTION AUDIO SELON CISCO INSTRUCTIONS - TOUS LES FICHIERS NUIT
  const AUDIO_CONFIG = {
    night: {
      files: [
        '/sounds/nuit-profonde/night-atmosphere-with-crickets-374652.mp3',
        '/sounds/nuit-profonde/hibou-molkom.mp3',
        '/sounds/nuit-profonde/sounds-crickets-nuit_profonde.mp3'
      ],
      volumes: [0.6, 0.35, 0.4]
    },
    sunrise: {
      files: [
        '/sounds/aube/village_morning_birds_roosters.mp3',
        '/sounds/lever-soleil/blackbird.mp3',
        '/sounds/lever-soleil/Lever_soleil-nature.mp3'
      ],
      volumes: [0.3, 0.4, 0.35]
    },
    fadeOutDuration: 8000,  // 8 secondes de fade out
    fadeInDuration: 3000    // 3 secondes de fade in
  } as const;

  // 🎵 GESTION AUDIO PROGRESSIVE AVEC FONDUES ET DEBUG
  const playNightSound = () => {
    console.log('🌙 CISCO: playNightSound() appelée - Démarrage sons de nuit');

    // Stop sunrise audios
    sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    sunriseAudioRefs.current = [];

    // Stop existing night audios
    nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    nightAudioRefs.current = [];

    // 🔧 CISCO: Sons de nuit avec FONDU D'ENTRÉE immédiat + DEBUG
    AUDIO_CONFIG.night.files.forEach((file, idx) => {
      console.log(`🌙 CISCO: Tentative de chargement: ${file}`);

      const a = new Audio(file);
      a.loop = true;
      a.volume = 0; // Démarrer à 0 pour fondu d'entrée

      // Gestion des événements audio pour debug
      a.addEventListener('loadstart', () => console.log(`🌙 CISCO: Chargement démarré: ${file}`));
      a.addEventListener('canplay', () => console.log(`🌙 CISCO: Prêt à jouer: ${file}`));
      a.addEventListener('error', (e) => console.error(`🌙 CISCO: ERREUR audio: ${file}`, e));

      // Tentative de lecture avec gestion d'erreur détaillée
      a.play().then(() => {
        console.log(`🌙 CISCO: Lecture réussie: ${file}`);

        // FONDU D'ENTRÉE de 3 secondes
        gsap.to(a, {
          volume: AUDIO_CONFIG.night.volumes[idx] ?? 0.5,
          duration: 3,
          ease: "power1.inOut",
          onComplete: () => console.log(`🌙 CISCO: Fondu d'entrée terminé: ${file}`)
        });

      }).catch((error) => {
        console.error(`🌙 CISCO: ÉCHEC lecture audio: ${file}`, error);
      });

      nightAudioRefs.current.push(a);
    });

    console.log(`🌙 CISCO: ${AUDIO_CONFIG.night.files.length} fichiers audio de nuit traités`);
  };

  // 🔧 CISCO: Ancienne fonction playSunriseSound supprimée
  // Remplacée par système audio avec keyframes précises dans la timeline

  // 🎬 CISCO: TIMELINE MAÎTRE AVEC KEYFRAMES CORRIGÉS
  const createMasterTimeline = () => {
    console.log('🎬 CISCO: Création timeline maître avec keyframes RALENTIS');

    // Tuer l'ancienne timeline si elle existe
    if (masterTimelineRef.current) {
      masterTimelineRef.current.kill();
    }

    // Créer la nouvelle timeline maître
    masterTimelineRef.current = gsap.timeline({
      paused: true,
      onComplete: () => {
        console.log('🌅 CISCO: Timeline complète - Jour complet atteint');
      }
    });

    const tl = masterTimelineRef.current;
    const duration = timerDuration; // 120 secondes par défaut

    // 🌙 KEYFRAME 0s : DÉBUT - Nuit profonde
    tl.set([containerRef.current, sunRef.current, moonRef.current, globalLightRef.current], {
      clearProps: "all"
    }, 0);

    // Position initiale de la lune
    tl.set(moonRef.current, {
      y: "120%",
      left: "10%",
      opacity: 1,
      width: "140px",
      height: "140px"
    }, 0);

    // Position initiale du soleil (invisible, bas à droite pour course longue)
    // 🔧 CISCO: RÉGLAGES PRÉCIS DU SOLEIL - COULEURS PASTELS NATURELLES :
    // =====================================================================
    // LIGNE 158 : right: "15%" = Position horizontale de départ (15% = à droite)
    // LIGNE 159 : bottom: "-30%" = Position verticale de départ (pas trop bas pour course longue)
    // LIGNE 160 : opacity: 0 = Invisibilité de départ (0 = invisible, 1 = visible)
    // LIGNE 161 : width: "70px" = Taille plus petite et naturelle
    // =====================================================================
    tl.set(sunRef.current, {
      right: "35%",   // 🔧 CISCO: LIGNE 158 - Position horizontale CENTRE-DROITE au départ
      bottom: "-30%", // 🔧 CISCO: LIGNE 159 - Position verticale de départ (pas trop bas)
      opacity: 0,     // 🔧 CISCO: LIGNE 160 - Invisibilité de départ
      width: "60px",  // 🔧 CISCO: LIGNE 161 - Plus petit
      height: "60px", // 🔧 CISCO: LIGNE 161 - Plus petit
      borderRadius: "50%", // CISCO: ROND !
      background: `radial-gradient(circle,
        #FFFFFF 0%,
        #FFFFFF 30%,
        #FFFEF8 60%,
        #F8F8F8 90%,
        rgba(255,255,255,0.5) 100%)`,
      boxShadow: `
        0 0 80px 25px rgba(255,255,255,1.0),
        0 0 120px 35px rgba(255,255,255,0.8),
        0 0 160px 45px rgba(255,255,255,0.6)`,
      filter: 'brightness(1.8)'
    }, 0);

    // Dégradé initial (nuit)
    tl.set(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.NIGHT_TOP}, ${SUNRISE_COLORS.NIGHT_BOTTOM})`
    }, 0);

    // 🌙 KEYFRAME 0s → 260s : Mouvement de la lune ULTRA RALENTI (RESTAURÉ)
    // 🔧 CISCO: KEYFRAMES PRÉCISES (Chronomètre officiel) :
    // ========================================================================
    // 00:00:56.352 = MOMENT où la lune passe derrière le paysage (pas la durée !)
    // 00:01:31.643 = MOMENT où le soleil se lève
    // La lune continue son mouvement lent sur 260s comme avant
    // ========================================================================
    tl.to(moonRef.current, {
      y: "1200%",
      left: "150%",
      duration: 260, // 🔧 CISCO: DURÉE RESTAURÉE - Lune lente comme avant
      ease: "none"
    }, 0);

    // 🎨 KEYFRAME 45s : Début changement dégradé (quand lune à mi-parcours)
    tl.to(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.SUNRISE_TOP}, ${SUNRISE_COLORS.SUNRISE_BOTTOM})`,
      duration: 30,
      ease: "power1.inOut"
    }, 45);

    console.log('🎨 CISCO: DÉGRADÉ DÉMARRE À 45 SECONDES');

    // ☀️ KEYFRAME 70s : SOLEIL LEVER PLUS TÔT - 1MIN10 !
    // 🔧 CISCO: KEYFRAMES CORRIGÉES (Écart réduit avec la lune) :
    // =====================================================================
    // 00:01:10 = 70 secondes = Soleil lever (14 secondes après la lune)
    // Lune: 56s, Soleil: 70s = Écart de 14s (au lieu de 35s)
    // =====================================================================
    tl.to(sunRef.current, {
      right: "15%",    // 🔧 CISCO: Position finale DROITE (zénith)
      bottom: "75%",   // 🔧 CISCO: Position ZÉNITH pour réalisme
      opacity: 1,      // 🔧 CISCO: Visibilité du soleil (0-1)
      width: "60px",   // 🔧 CISCO: Plus petit
      height: "60px",  // 🔧 CISCO: Plus petit
      borderRadius: "50%", // CISCO: ROND !
      background: `radial-gradient(circle,
        #FFFFFF 0%,
        #FFFFFF 30%,
        #FFFEF8 60%,
        #F8F8F8 90%,
        rgba(255,255,255,0.5) 100%)`,
      boxShadow: `
        0 0 80px 25px rgba(255,255,255,1.0),
        0 0 120px 35px rgba(255,255,255,0.8),
        0 0 160px 45px rgba(255,255,255,0.6)`,
      filter: 'brightness(1.8)',
      duration: 100,   // 🔧 CISCO: DURÉE RESTAURÉE comme avant
      ease: "power1.out"
    }, 70); // 🔧 CISCO: KEYFRAME CORRIGÉE - Soleil lever à 1min10 (70s)

    console.log('☀️ CISCO: SOLEIL DÉMARRE À 70s (1min10) - ÉCART RÉDUIT AVEC LUNE');

    // 🌟 CISCO: ANIMATION PULSATION SOLEIL BLANC LUMINEUX (après apparition)
    tl.to(sunRef.current, {
      scale: 1.04,
      filter: 'brightness(1.8)',
      duration: 5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    }, 90); // Démarre la pulsation 20 secondes après l'apparition

    // 💡 KEYFRAME 75s : Début éclairage global
    tl.to(globalLightRef.current, {
      opacity: 0.6, // CISCO: Moins fort
      duration: 25,
      ease: "power1.inOut"
    }, 75);

    // 🌄 KEYFRAME 75s : Éclairage du paysage DÉFINITIVEMENT PERMANENT
    // 🔧 CISCO: POUR RÉGLER L'ÉCLAIRAGE DU PAYSAGE, MODIFIEZ LES LIGNES CI-DESSOUS :
    // ==============================================================================
    // LIGNE 233 : filter: 'brightness(0.3)' = Luminosité de départ (0.1 = très sombre, 0.5 = normal)
    // LIGNE 235 : filter: 'brightness(0.8)' = Luminosité finale (0.5 = sombre, 1.0 = normal, 1.5 = très clair)
    // LIGNE 236 : duration: 45 = Durée de l'éclairage (30 = rapide, 45 = normal, 90 = très lent)
    // LIGNE 254 : }, [], 75 = Moment de démarrage (75 = 75 secondes après le début)
    // ==============================================================================
    tl.call(() => {
      // CISCO: Éclairage PERMANENT du paysage Background.png
      const landscapeElements = document.querySelectorAll('[style*="Background.png"]');
      landscapeElements.forEach(element => {
        // Démarrer PLUS sombre puis éclairer PROGRESSIVEMENT selon soleil
        gsap.set(element, { filter: 'brightness(0.08)' }); // 🔧 CISCO: LIGNE 233 - LUMINOSITÉ DE DÉPART PLUS SOMBRE

        // PHASE 1 : Éclairage progressif jusqu'au lever du soleil (55s)
        gsap.to(element, {
          filter: 'brightness(0.25)', // Éclairage modéré jusqu'au soleil
          duration: 35, // Jusqu'à l'arrivée du soleil (75s + 35s = 110s)
          ease: "power1.inOut",
        });

        // PHASE 2 : Éclairage fort quand soleil au zénith (110s+)
        gsap.to(element, {
          filter: 'brightness(0.9)', // 🔧 CISCO: LIGNE 235 - LUMINOSITÉ FINALE FORTE
          duration: 30, // Éclairage fort rapide
          ease: "power1.inOut",
          delay: 35, // Démarre après la phase 1
          // 🔧 CISCO: FORCER L'ÉTAT PERMANENT - Éclairage fort final
          onComplete: () => {
            (element as HTMLElement).style.filter = 'brightness(0.9) !important';
            // CISCO: Bloquer toute modification future
            (element as HTMLElement).style.setProperty('filter', 'brightness(0.9)', 'important');
            console.log('🌄 CISCO: Paysage DÉFINITIVEMENT éclairé FORT avec !important');
          }
        });
      });

      // CISCO: Sécurité supplémentaire - Forcer l'éclairage toutes les 5 secondes
      setInterval(() => {
        landscapeElements.forEach(element => {
          if ((element as HTMLElement).style.filter !== 'brightness(0.9)') {
            (element as HTMLElement).style.setProperty('filter', 'brightness(0.9)', 'important');
            console.log('🌄 CISCO: Paysage re-éclairé FORT par sécurité');
          }
        });
      }, 5000);

      console.log('🌄 CISCO: Éclairage paysage PERMANENT démarré (45s) + sécurité');
    }, [], 75); // 🔧 CISCO: LIGNE 254 - MOMENT DE DÉMARRAGE DE L'ÉCLAIRAGE (secondes)

    // ☁️ KEYFRAME 45s : COLORATION NUAGES SELON DÉGRADÉ - SYNCHRONISÉ PARFAIT
    tl.call(() => {
      // CISCO: SYSTÈME RÉVOLUTIONNAIRE - Nuages prennent la couleur du dégradé !
      const cloudImages = document.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;

      // PHASE 1 (45s-75s) : Nuit → Lever (Rose/Bleu)
      cloudImages.forEach(cloudImg => {
        gsap.set(cloudImg, {
          filter: 'brightness(0.3) saturate(0.6) contrast(0.8) hue-rotate(0deg)'
        });
        gsap.to(cloudImg, {
          filter: 'brightness(0.7) saturate(1.2) contrast(1.1) hue-rotate(8deg) sepia(0.15)',
          duration: 30, // Phase lever de soleil
          ease: "power1.inOut"
        });
      });
      console.log('☁️ CISCO: PHASE 1 - Nuages prennent couleur LEVER (rose/bleu)');
    }, [], 45); // Démarre avec le dégradé

    // ☁️ KEYFRAME 90s : NUAGES BLANCS JOUR COMPLET - RÉALISTE
    tl.call(() => {
      const cloudImages = document.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;

      // PHASE 2 (90s+) : Lever → Jour (NUAGES BLANCS - pas bleus !)
      cloudImages.forEach(cloudImg => {
        gsap.to(cloudImg, {
          filter: 'brightness(1.2) saturate(0.8) contrast(1.1) hue-rotate(0deg)',
          duration: 20, // Phase jour complet
          ease: "power1.inOut"
        });
      });
      console.log('☁️ CISCO: PHASE 2 - Nuages deviennent BLANCS (réaliste)');
    }, [], 90); // Démarre avec le dégradé jour

    // ⭐ KEYFRAME 85s : Disparition des étoiles (quand lune presque disparue)
    tl.to(starsContainerRef.current, {
      opacity: 0,
      duration: 20,
      ease: "power2.out"
    }, 85);

    // 🎨 KEYFRAME 90s : Dégradé jour complet
    tl.to(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.DAY_TOP}, ${SUNRISE_COLORS.DAY_BOTTOM})`,
      duration: 20,
      ease: "power1.inOut"
    }, 90);

    // 🎵 KEYFRAMES AUDIO PRÉCISES AVEC FONDUES
    // 🔧 CISCO: Audio synchronisé avec keyframes chronomètre
    tl.call(playNightSound, [], 0); // Démarrage immédiat sons de nuit avec fondu d'entrée

    // 🌙 KEYFRAME 56.352s : Lune passe derrière paysage → FADE OUT nuit
    tl.call(() => {
      // Fade out progressif des sons de nuit quand lune se couche
      nightAudioRefs.current.forEach(audio => {
        gsap.to(audio, {
          volume: 0,
          duration: 8, // 8 secondes de fade out doux
          ease: "power1.inOut",
          onComplete: () => audio.pause()
        });
      });
      console.log('🌙 CISCO: FADE OUT sons nuit à 56.352s (lune coucher)');
    }, [], 56.352);

    // ☀️ KEYFRAME 70s : Soleil lever → FADE IN aube/lever
    tl.call(() => {
      // Démarrer les sons d'aube/lever avec fade in doux
      const aubeFiles = [
        '/sounds/aube/village_morning_birds_roosters.mp3',
        '/sounds/lever-soleil/blackbird.mp3',
        '/sounds/lever-soleil/Lever_soleil-nature.mp3'
      ];
      const aubeVolumes = [0.3, 0.4, 0.35];

      aubeFiles.forEach((file, idx) => {
        const audio = new Audio(file);
        audio.loop = true;
        audio.volume = 0;
        audio.play().catch(console.warn);

        gsap.to(audio, {
          volume: aubeVolumes[idx] ?? 0.3,
          duration: 6, // 6 secondes de fade in doux
          ease: "power1.inOut"
        });

        sunriseAudioRefs.current.push(audio);
      });
      console.log('☀️ CISCO: FADE IN sons aube/lever à 70s (1min10 - soleil lever)');
    }, [], 70);

    console.log(`🎬 CISCO: Timeline KEYFRAMES PRÉCISES créée - Durée totale: ${duration}s`);
    console.log(`🌙 CISCO: Lune coucher à 56.352s - FADE OUT sons nuit`);
    console.log(`☀️ CISCO: Soleil lever à 91.643s - FADE IN sons aube/lever`);
    console.log(`🎨 CISCO: DÉGRADÉ DÉMARRE À 45 SECONDES`);
    console.log(`🎵 CISCO: AUDIO avec fondues douces synchronisées`);
    return tl;
  };

  // 🔄 EFFET PRINCIPAL - ACTIVATION/DÉSACTIVATION
  useEffect(() => {
    if (isActive) {
      console.log('🌅 CISCO: ACTIVATION MODULE LEVER DE SOLEIL - Timeline avec keyframes');
      
      const timeline = createMasterTimeline();
      timeline.play();
      
    } else {
      console.log('🌅 CISCO: DÉSACTIVATION MODULE LEVER DE SOLEIL');
      
      // Arrêter la timeline
      if (masterTimelineRef.current) {
        masterTimelineRef.current.kill();
      }
      
      // Arrêter tous les audios
      [...nightAudioRefs.current, ...sunriseAudioRefs.current].forEach(audio => {
        try { audio.pause(); } catch {}
      });
      nightAudioRefs.current = [];
      sunriseAudioRefs.current = [];
    }

    // Nettoyage au démontage
    return () => {
      if (masterTimelineRef.current) {
        masterTimelineRef.current.kill();
      }
      [...nightAudioRefs.current, ...sunriseAudioRefs.current].forEach(audio => {
        try { audio.pause(); } catch {}
      });
    };
  }, [isActive, timerDuration]);

  return (
    <>
      {/* 🌅 CISCO: CONTENEUR PRINCIPAL */}
      <div
        ref={containerRef}
        className="fixed inset-0 pointer-events-none"
        style={{
          zIndex: 5,
          opacity: isActive ? 1 : 0
        }}
      >
      {/* 🌙 LUNE AVEC HALO DISCRET */}
      <img
        ref={moonRef}
        src="/Lune-Moon.png"
        alt="Lune"
        className="absolute"
        style={{
          width: '140px',
          height: '140px',
          objectFit: 'contain',
          opacity: 1,
          zIndex: 2,
          pointerEvents: 'none',
          userSelect: 'none',
          filter: 'drop-shadow(0 0 35px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 70px rgba(220, 220, 255, 0.3))',
          borderRadius: '50%'
        }}
      />

      {/* 🌞 SOLEIL PASTEL NATUREL - PETIT ET LUMINEUX */}
      <div
        ref={sunRef}
        className="absolute"
        style={{
          right: '35%',        // CISCO: CENTRE-DROITE au départ pour trajectoire réaliste
          bottom: '75%',       // CISCO: ZÉNITH RÉALISTE pour effet naturel
          transform: 'translate(50%, 50%)',
          width: '60px',       // 🔧 CISCO: Plus petit
          height: '60px',      // 🔧 CISCO: Plus petit
          borderRadius: '50%', // CISCO: ROND (pas carré !)
          background: `radial-gradient(circle,
            #FFFFFF 0%,
            #FFFFFF 30%,
            #FFFEF8 60%,
            #F8F8F8 90%,
            rgba(255,255,255,0.5) 100%)`,
          boxShadow: `
            0 0 80px 25px rgba(255,255,255,1.0),
            0 0 120px 35px rgba(255,255,255,0.8),
            0 0 160px 45px rgba(255,255,255,0.6)`,
          filter: 'brightness(1.8)',
          opacity: 0,
          zIndex: 15,          // CISCO: Z-index plus élevé pour être au-dessus de tout
          pointerEvents: 'none',
          userSelect: 'none'
        }}
      />

      {/* ⭐ ÉTOILES UNIFIÉES */}
      <div ref={starsContainerRef}>
        <UnifiedStars
          skyMode="leverSoleil"
          isVisible={isActive}
          opacity={1}
        />
      </div>

      {/* 💡 ÉCLAIRAGE GLOBAL PROGRESSIF */}
      <div
        ref={globalLightRef}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at 75% 60%,
            #FFE9B015 0%,
            #FFF8DC08 30%,
            #FFFDF505 50%,
            transparent 75%)`,
          opacity: 0,
          zIndex: 6
        }}
      />

      </div>
    </>
  );
};

export default ModeLeverSoleil;
