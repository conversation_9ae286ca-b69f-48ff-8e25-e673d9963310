import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
// 🌟 CISCO: ÉTOILES UNIFIÉES - SYSTÈME COMPLET
import UnifiedStars from './UnifiedStars';

interface ModeLeverSoleilProps {
  isActive: boolean;
  timerDuration?: number; // Durée du temporisateur en secondes (défaut: 120s)
}

/**
 * 🌅 CISCO: SYSTÈME TIMELINE AVEC KEYFRAMES
 * 
 * NOUVEAU SYSTÈME RÉVOLUTIONNAIRE :
 * - Une seule timeline maître qui orchestre TOUT
 * - Keyframes temporels précis (8s, 10s, 15s, etc.)
 * - Synchronisation parfaite de tous les éléments
 * - Plus de calculs complexes de pourcentages
 * - Contrôle exact du timing
 */
const ModeLeverSoleil: React.FC<ModeLeverSoleilProps> = ({
  isActive,
  timerDuration = 120 // 120s VITESSE NORMALE
}) => {
  // 🎯 RÉFÉRENCES POUR LES ÉLÉMENTS
  const containerRef = useRef<HTMLDivElement>(null);
  const sunRef = useRef<HTMLDivElement>(null);
  const moonRef = useRef<HTMLImageElement>(null);
  const starsContainerRef = useRef<HTMLDivElement>(null);
  const globalLightRef = useRef<HTMLDivElement>(null);
  const masterTimelineRef = useRef<gsap.core.Timeline | null>(null);
  
  // 🎵 Références audio multiples pour mixer les ambiances
  const nightAudioRefs = useRef<HTMLAudioElement[]>([]);
  const sunriseAudioRefs = useRef<HTMLAudioElement[]>([]);

  // 🌅 CISCO: DÉGRADÉ LEVER DE SOLEIL - Rose en bas, bleu en haut
  const SUNRISE_COLORS = {
    // Début : Nuit profonde
    NIGHT_TOP: '#001540',        // Bleu nuit profond (top)
    NIGHT_BOTTOM: '#001540',     // Bleu nuit profond (bottom)
    // Lever : Rose en bas, bleu clair en haut
    SUNRISE_TOP: '#4682B4',      // Bleu acier (top)
    SUNRISE_BOTTOM: '#FFC0CB',   // Rose (bottom)
    // Jour : Bleu ciel
    DAY_TOP: '#6ab0f1ff',          // Bleu ciel clair (top)
    DAY_BOTTOM: '#e0f3ffff'        // Bleu très clair (bottom)
  };

  // 🎵 GESTION AUDIO SELON CISCO INSTRUCTIONS
  const AUDIO_CONFIG = {
    night: {
      files: [
        '/sounds/nuit-profonde/night-atmosphere-with-crickets-374652.mp3',
        '/sounds/nuit-profonde/hibou-molkom.mp3'
      ],
      volumes: [0.6, 0.35]
    },
    sunrise: {
      files: [
        '/sounds/aube/village_morning_birds_roosters.mp3',
        '/sounds/lever-soleil/blackbird.mp3'
      ],
      volumes: [0.3, 0.4]
    },
    fadeOutDuration: 5000,  // 5 secondes de fade out
    fadeInDuration: 3000    // 3 secondes de fade in
  } as const;

  // 🎵 GESTION AUDIO PROGRESSIVE
  const playNightSound = () => {
    // Stop sunrise audios
    sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    sunriseAudioRefs.current = [];

    // Stop existing night audios
    nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    nightAudioRefs.current = [];

    AUDIO_CONFIG.night.files.forEach((file, idx) => {
      const a = new Audio(file);
      a.loop = true;
      a.volume = AUDIO_CONFIG.night.volumes[idx] ?? 0.5;
      a.play().catch(console.warn);
      nightAudioRefs.current.push(a);
    });
    console.log('🌙 Sons de nuit démarrés (criquets + hibou)');
  };

  const playSunriseSound = () => {
    // Fade out night sounds
    nightAudioRefs.current.forEach(audio => {
      gsap.to(audio, {
        volume: 0,
        duration: AUDIO_CONFIG.fadeOutDuration / 1000,
        ease: "power1.inOut",
        onComplete: () => audio.pause()
      });
    });

    // Sunrise sounds
    AUDIO_CONFIG.sunrise.files.forEach((file, idx) => {
      const audio = new Audio(file);
      audio.loop = true;
      audio.volume = 0;
      audio.play().catch(console.warn);
      
      gsap.to(audio, {
        volume: AUDIO_CONFIG.sunrise.volumes[idx] ?? 0.3,
        duration: AUDIO_CONFIG.fadeInDuration / 1000,
        ease: "power1.inOut"
      });
      
      sunriseAudioRefs.current.push(audio);
    });

    console.log('🌅 Sons de lever de soleil démarrés');
  };

  // 🎬 CISCO: TIMELINE MAÎTRE AVEC KEYFRAMES CORRIGÉS
  const createMasterTimeline = () => {
    console.log('🎬 CISCO: Création timeline maître avec keyframes RALENTIS');

    // Tuer l'ancienne timeline si elle existe
    if (masterTimelineRef.current) {
      masterTimelineRef.current.kill();
    }

    // Créer la nouvelle timeline maître
    masterTimelineRef.current = gsap.timeline({
      paused: true,
      onComplete: () => {
        console.log('🌅 CISCO: Timeline complète - Jour complet atteint');
      }
    });

    const tl = masterTimelineRef.current;
    const duration = timerDuration; // 120 secondes par défaut

    // 🌙 KEYFRAME 0s : DÉBUT - Nuit profonde
    tl.set([containerRef.current, sunRef.current, moonRef.current, globalLightRef.current], {
      clearProps: "all"
    }, 0);

    // Position initiale de la lune
    tl.set(moonRef.current, {
      y: "120%",
      left: "10%",
      opacity: 1,
      width: "140px",
      height: "140px"
    }, 0);

    // Position initiale du soleil (invisible, bas à droite pour course longue)
    // 🔧 CISCO: RÉGLAGES PRÉCIS DU SOLEIL - COULEURS PASTELS NATURELLES :
    // =====================================================================
    // LIGNE 158 : right: "15%" = Position horizontale de départ (15% = à droite)
    // LIGNE 159 : bottom: "-30%" = Position verticale de départ (pas trop bas pour course longue)
    // LIGNE 160 : opacity: 0 = Invisibilité de départ (0 = invisible, 1 = visible)
    // LIGNE 161 : width: "70px" = Taille plus petite et naturelle
    // =====================================================================
    tl.set(sunRef.current, {
      right: "15%",   // 🔧 CISCO: LIGNE 158 - Position horizontale de départ
      bottom: "-30%", // 🔧 CISCO: LIGNE 159 - Position verticale de départ (pas trop bas)
      opacity: 0,     // 🔧 CISCO: LIGNE 160 - Invisibilité de départ
      width: "70px",  // 🔧 CISCO: LIGNE 161 - Plus petit et naturel
      height: "70px", // 🔧 CISCO: LIGNE 161 - Plus petit et naturel
      borderRadius: "50%", // CISCO: ROND !
      background: `radial-gradient(circle,
        #FFFFFF 0%,
        #FFFEF8 20%,
        #FFF8F0 40%,
        #F5F5F5 70%,
        rgba(255,255,255,0.4) 100%)`,
      boxShadow: `
        0 0 60px 20px rgba(255,255,255,0.9),
        0 0 100px 30px rgba(255,255,255,0.7),
        0 0 140px 40px rgba(255,255,255,0.5)`,
      filter: 'brightness(1.6)'
    }, 0);

    // Dégradé initial (nuit)
    tl.set(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.NIGHT_TOP}, ${SUNRISE_COLORS.NIGHT_BOTTOM})`
    }, 0);

    // 🌙 KEYFRAME 0s → 260s : Mouvement de la lune ULTRA RALENTI
    // 🔧 CISCO: POUR RÉGLER LA VITESSE DE LA LUNE, MODIFIEZ LA LIGNE CI-DESSOUS :
    // ========================================================================
    // LIGNE 179 : duration: 260 = 260 secondes (plus le chiffre est grand, plus c'est lent)
    // Exemples: 180 = rapide, 260 = normal, 360 = très lent
    // ========================================================================
    tl.to(moonRef.current, {
      y: "1200%",
      left: "150%",
      duration: 260, // 🔧 CISCO: LIGNE 179 - MODIFIEZ ICI LA VITESSE DE LA LUNE
      ease: "none"
    }, 0);

    // 🎨 KEYFRAME 45s : Début changement dégradé (quand lune à mi-parcours)
    tl.to(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.SUNRISE_TOP}, ${SUNRISE_COLORS.SUNRISE_BOTTOM})`,
      duration: 30,
      ease: "power1.inOut"
    }, 45);

    console.log('🎨 CISCO: DÉGRADÉ DÉMARRE À 45 SECONDES');

    // ☀️ KEYFRAME 55s : DÉCLENCHEMENT SOLEIL ENCORE PLUS TÔT - ZÉNITH RÉALISTE
    // 🔧 CISCO: RÉGLAGES PRÉCIS DU SOLEIL - MODIFIEZ LES LIGNES CI-DESSOUS :
    // =====================================================================
    // LIGNE 199 : right: "15%" = Position horizontale (15% = à droite, 50% = centre)
    // LIGNE 200 : bottom: "75%" = Position verticale ZÉNITH pour réalisme
    // LIGNE 201 : opacity: 1 = Visibilité (0 = invisible, 1 = visible)
    // LIGNE 206 : duration: 100 = Vitesse LENTE pour être visible
    // LIGNE 208 : }, 55 = Moment de démarrage (55s = 10s PLUS TÔT)
    // =====================================================================
    tl.to(sunRef.current, {
      right: "15%",    // 🔧 CISCO: LIGNE 199 - Position horizontale du soleil
      bottom: "75%",   // 🔧 CISCO: LIGNE 200 - Position ZÉNITH pour réalisme
      opacity: 1,      // 🔧 CISCO: LIGNE 201 - Visibilité du soleil (0-1)
      width: "70px",   // 🔧 CISCO: Plus petit et naturel
      height: "70px",  // 🔧 CISCO: Plus petit et naturel
      borderRadius: "50%", // CISCO: ROND !
      background: `radial-gradient(circle,
        #FFFFFF 0%,
        #FFFEF8 20%,
        #FFF8F0 40%,
        #F5F5F5 70%,
        rgba(255,255,255,0.4) 100%)`,
      boxShadow: `
        0 0 60px 20px rgba(255,255,255,0.9),
        0 0 100px 30px rgba(255,255,255,0.7),
        0 0 140px 40px rgba(255,255,255,0.5)`,
      filter: 'brightness(1.6)',
      duration: 100,   // 🔧 CISCO: LIGNE 206 - VITESSE LENTE
      ease: "power1.out"
    }, 55); // 🔧 CISCO: LIGNE 208 - DÉMARRAGE À 55s (10s PLUS TÔT)

    console.log('☀️ CISCO: SOLEIL DÉMARRE À 55 SECONDES - ZÉNITH 75% - BLANC ULTRA-LUMINEUX');

    // 🌟 CISCO: ANIMATION PULSATION SOLEIL BLANC LUMINEUX (après apparition)
    tl.to(sunRef.current, {
      scale: 1.04,
      filter: 'brightness(1.8)',
      duration: 5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    }, 75); // Démarre la pulsation 20 secondes après l'apparition

    // 💡 KEYFRAME 75s : Début éclairage global
    tl.to(globalLightRef.current, {
      opacity: 0.6, // CISCO: Moins fort
      duration: 25,
      ease: "power1.inOut"
    }, 75);

    // 🌄 KEYFRAME 75s : Éclairage du paysage DÉFINITIVEMENT PERMANENT
    // 🔧 CISCO: POUR RÉGLER L'ÉCLAIRAGE DU PAYSAGE, MODIFIEZ LES LIGNES CI-DESSOUS :
    // ==============================================================================
    // LIGNE 233 : filter: 'brightness(0.3)' = Luminosité de départ (0.1 = très sombre, 0.5 = normal)
    // LIGNE 235 : filter: 'brightness(0.8)' = Luminosité finale (0.5 = sombre, 1.0 = normal, 1.5 = très clair)
    // LIGNE 236 : duration: 45 = Durée de l'éclairage (30 = rapide, 45 = normal, 90 = très lent)
    // LIGNE 254 : }, [], 75 = Moment de démarrage (75 = 75 secondes après le début)
    // ==============================================================================
    tl.call(() => {
      // CISCO: Éclairage PERMANENT du paysage Background.png
      const landscapeElements = document.querySelectorAll('[style*="Background.png"]');
      landscapeElements.forEach(element => {
        // Démarrer TRÈS sombre puis éclairer DÉFINITIVEMENT
        gsap.set(element, { filter: 'brightness(0.15)' }); // 🔧 CISCO: LIGNE 233 - LUMINOSITÉ DE DÉPART TRÈS SOMBRE
        gsap.to(element, {
          filter: 'brightness(0.8)', // 🔧 CISCO: LIGNE 235 - LUMINOSITÉ FINALE
          duration: 45, // 🔧 CISCO: LIGNE 236 - DURÉE DE L'ÉCLAIRAGE (secondes)
          ease: "power1.inOut",
          // 🔧 CISCO: FORCER L'ÉTAT PERMANENT - Ne jamais revenir sombre
          onComplete: () => {
            (element as HTMLElement).style.filter = 'brightness(0.8) !important';
            // CISCO: Bloquer toute modification future
            (element as HTMLElement).style.setProperty('filter', 'brightness(0.8)', 'important');
            console.log('🌄 CISCO: Paysage DÉFINITIVEMENT éclairé avec !important');
          }
        });
      });

      // CISCO: Sécurité supplémentaire - Forcer l'éclairage toutes les 5 secondes
      setInterval(() => {
        landscapeElements.forEach(element => {
          if ((element as HTMLElement).style.filter !== 'brightness(0.8)') {
            (element as HTMLElement).style.setProperty('filter', 'brightness(0.8)', 'important');
            console.log('🌄 CISCO: Paysage re-éclairé par sécurité');
          }
        });
      }, 5000);

      console.log('🌄 CISCO: Éclairage paysage PERMANENT démarré (45s) + sécurité');
    }, [], 75); // 🔧 CISCO: LIGNE 254 - MOMENT DE DÉMARRAGE DE L'ÉCLAIRAGE (secondes)

    // ☁️ KEYFRAME 45s : COLORATION NUAGES SELON DÉGRADÉ - SYNCHRONISÉ PARFAIT
    tl.call(() => {
      // CISCO: SYSTÈME RÉVOLUTIONNAIRE - Nuages prennent la couleur du dégradé !
      const cloudImages = document.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;

      // PHASE 1 (45s-75s) : Nuit → Lever (Rose/Bleu)
      cloudImages.forEach(cloudImg => {
        gsap.set(cloudImg, {
          filter: 'brightness(0.3) saturate(0.6) contrast(0.8) hue-rotate(0deg)'
        });
        gsap.to(cloudImg, {
          filter: 'brightness(0.7) saturate(1.2) contrast(1.1) hue-rotate(8deg) sepia(0.15)',
          duration: 30, // Phase lever de soleil
          ease: "power1.inOut"
        });
      });
      console.log('☁️ CISCO: PHASE 1 - Nuages prennent couleur LEVER (rose/bleu)');
    }, [], 45); // Démarre avec le dégradé

    // ☁️ KEYFRAME 90s : COLORATION JOUR COMPLET - NUAGES CLAIRS
    tl.call(() => {
      const cloudImages = document.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;

      // PHASE 2 (90s+) : Lever → Jour (Bleu ciel clair)
      cloudImages.forEach(cloudImg => {
        gsap.to(cloudImg, {
          filter: 'brightness(1.0) saturate(1.0) contrast(1.0) hue-rotate(0deg)',
          duration: 20, // Phase jour complet
          ease: "power1.inOut"
        });
      });
      console.log('☁️ CISCO: PHASE 2 - Nuages prennent couleur JOUR (bleu ciel)');
    }, [], 90); // Démarre avec le dégradé jour

    // ⭐ KEYFRAME 85s : Disparition des étoiles (quand lune presque disparue)
    tl.to(starsContainerRef.current, {
      opacity: 0,
      duration: 20,
      ease: "power2.out"
    }, 85);

    // 🎨 KEYFRAME 90s : Dégradé jour complet
    tl.to(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.DAY_TOP}, ${SUNRISE_COLORS.DAY_BOTTOM})`,
      duration: 20,
      ease: "power1.inOut"
    }, 90);

    // 🎵 KEYFRAMES AUDIO
    tl.call(playNightSound, [], 0);
    tl.call(playSunriseSound, [], 85); // Audio lever quand étoiles disparaissent

    console.log(`🎬 CISCO: Timeline ULTRA-RALENTIE créée - Durée totale: ${duration}s`);
    console.log(`🌙 CISCO: Lune descend sur 260s (TRÈS lent)`);
    console.log(`🎨 CISCO: DÉGRADÉ DÉMARRE À 45 SECONDES`);
    console.log(`☀️ CISCO: SOLEIL DÉMARRE À 55 SECONDES - NUAGES COLORÉS SELON DÉGRADÉ`);
    return tl;
  };

  // 🔄 EFFET PRINCIPAL - ACTIVATION/DÉSACTIVATION
  useEffect(() => {
    if (isActive) {
      console.log('🌅 CISCO: ACTIVATION MODULE LEVER DE SOLEIL - Timeline avec keyframes');
      
      const timeline = createMasterTimeline();
      timeline.play();
      
    } else {
      console.log('🌅 CISCO: DÉSACTIVATION MODULE LEVER DE SOLEIL');
      
      // Arrêter la timeline
      if (masterTimelineRef.current) {
        masterTimelineRef.current.kill();
      }
      
      // Arrêter tous les audios
      [...nightAudioRefs.current, ...sunriseAudioRefs.current].forEach(audio => {
        try { audio.pause(); } catch {}
      });
      nightAudioRefs.current = [];
      sunriseAudioRefs.current = [];
    }

    // Nettoyage au démontage
    return () => {
      if (masterTimelineRef.current) {
        masterTimelineRef.current.kill();
      }
      [...nightAudioRefs.current, ...sunriseAudioRefs.current].forEach(audio => {
        try { audio.pause(); } catch {}
      });
    };
  }, [isActive, timerDuration]);

  return (
    <>
      {/* 🌅 CISCO: CONTENEUR PRINCIPAL */}
      <div
        ref={containerRef}
        className="fixed inset-0 pointer-events-none"
        style={{
          zIndex: 5,
          opacity: isActive ? 1 : 0
        }}
      >
      {/* 🌙 LUNE AVEC HALO DISCRET */}
      <img
        ref={moonRef}
        src="/Lune-Moon.png"
        alt="Lune"
        className="absolute"
        style={{
          width: '140px',
          height: '140px',
          objectFit: 'contain',
          opacity: 1,
          zIndex: 2,
          pointerEvents: 'none',
          userSelect: 'none',
          filter: 'drop-shadow(0 0 35px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 70px rgba(220, 220, 255, 0.3))',
          borderRadius: '50%'
        }}
      />

      {/* 🌞 SOLEIL PASTEL NATUREL - PETIT ET LUMINEUX */}
      <div
        ref={sunRef}
        className="absolute"
        style={{
          right: '15%',        // CISCO: À DROITE (pas à gauche !)
          bottom: '75%',       // CISCO: ZÉNITH RÉALISTE pour effet naturel
          transform: 'translate(50%, 50%)',
          width: '70px',       // 🔧 CISCO: Plus petit et naturel
          height: '70px',      // 🔧 CISCO: Plus petit et naturel
          borderRadius: '50%', // CISCO: ROND (pas carré !)
          background: `radial-gradient(circle,
            #FFFFFF 0%,
            #FFFEF8 20%,
            #FFF8F0 40%,
            #F5F5F5 70%,
            rgba(255,255,255,0.4) 100%)`,
          boxShadow: `
            0 0 60px 20px rgba(255,255,255,0.9),
            0 0 100px 30px rgba(255,255,255,0.7),
            0 0 140px 40px rgba(255,255,255,0.5)`,
          filter: 'brightness(1.6)',
          opacity: 0,
          zIndex: 15,          // CISCO: Z-index plus élevé pour être au-dessus de tout
          pointerEvents: 'none',
          userSelect: 'none'
        }}
      />

      {/* ⭐ ÉTOILES UNIFIÉES */}
      <div ref={starsContainerRef}>
        <UnifiedStars
          skyMode="leverSoleil"
          isVisible={isActive}
          opacity={1}
        />
      </div>

      {/* 💡 ÉCLAIRAGE GLOBAL PROGRESSIF */}
      <div
        ref={globalLightRef}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at 75% 60%,
            #FFE9B015 0%,
            #FFF8DC08 30%,
            #FFFDF505 50%,
            transparent 75%)`,
          opacity: 0,
          zIndex: 6
        }}
      />

      </div>
    </>
  );
};

export default ModeLeverSoleil;
